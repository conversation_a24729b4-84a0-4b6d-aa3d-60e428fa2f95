
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not found');
    }

    const { title, mainCharacter, setting, plotIdea, ageGroup } = await req.json();

    // Create age-appropriate prompt
    const agePrompts = {
      '2-4': 'Write a very simple children\'s story for toddlers (2-4 years old). Use basic words, short sentences, and focus on simple concepts like colors, shapes, and familiar objects.',
      '5-7': 'Write a children\'s story for early readers (5-7 years old). Use simple vocabulary but with slightly longer sentences. Include basic lessons about friendship, kindness, or simple problem-solving.',
      '8-10': 'Write a children\'s story for independent readers (8-10 years old). Use more descriptive language and include character development with meaningful lessons about courage, perseverance, or helping others.'
    };

    const agePrompt = agePrompts[ageGroup as keyof typeof agePrompts] || agePrompts['5-7'];

    const prompt = `${agePrompt}

Story Details:
- Title: ${title}
- Main Character: ${mainCharacter}
- Setting: ${setting || 'an interesting place'}
- Plot: ${plotIdea}

Please write the story in exactly 6 short sections/pages. Each section should be 2-3 sentences that describe one scene or moment in the story. Format your response as a JSON object with this structure:
{
  "pages": [
    {"text": "Page 1 text here", "sceneDescription": "Brief description of what should be illustrated"},
    {"text": "Page 2 text here", "sceneDescription": "Brief description of what should be illustrated"},
    ... (6 pages total)
  ]
}

Make sure the story has a clear beginning, middle, and end with a positive message appropriate for children.`;

    console.log('Generating story with OpenAI...');
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'You are a creative children\'s book author who writes engaging, age-appropriate stories. Always respond with valid JSON in the requested format.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.8,
        max_tokens: 1000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const generatedContent = data.choices[0].message.content;
    
    let storyData;
    try {
      storyData = JSON.parse(generatedContent);
    } catch (e) {
      // Fallback if JSON parsing fails
      console.error('Failed to parse JSON response:', e);
      throw new Error('Failed to generate properly formatted story');
    }

    // Combine all page texts into a single story text
    const fullStoryText = storyData.pages.map((page: any) => page.text).join('\n\n');

    console.log('Story generated successfully');

    return new Response(JSON.stringify({
      success: true,
      storyText: fullStoryText,
      pages: storyData.pages
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-story function:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Failed to generate story'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
