
import React, { useState } from 'react';
import SplashScreen from '../components/SplashScreen';
import StoryWizard from '../components/wizard/StoryWizard';
import LibraryView from '../components/library/LibraryView';
import { AnimatePresence } from 'framer-motion';

const Index = () => {
  const [showSplash, setShowSplash] = useState(true);
  const [currentView, setCurrentView] = useState<'wizard' | 'library'>('wizard');

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  const handleWizardComplete = () => {
    setCurrentView('library');
  };

  const handleCreateNew = () => {
    setCurrentView('wizard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-light/50 via-white to-blue-light/30">
      <AnimatePresence>
        {showSplash && <SplashScreen onComplete={handleSplashComplete} />}
      </AnimatePresence>

      {!showSplash && (
        <div className="container mx-auto">
          {currentView === 'wizard' ? (
            <StoryWizard onComplete={handleWizardComplete} />
          ) : (
            <LibraryView onCreateNew={handleCreateNew} />
          )}
        </div>
      )}
    </div>
  );
};

export default Index;
