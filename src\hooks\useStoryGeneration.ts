import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { <PERSON><PERSON> } from '@/integrations/supabase/types';
import { useImageStorage } from './useImageStorage';

interface Character {
  name: string;
  description: string;
}

interface StoryFormData {
  title: string;
  characters: Character[];
  setting: string;
  plotIdea: string;
  ageGroup: string;
  language: string;
  numPages: number;
}

interface StoryPage {
  text: string;
  sceneDescription?: string;
  imageUrl?: string;
}

interface DialogueLine {
  speaker: string;
  line: string;
}

interface StoryScene {
  scene_id: string;
  location: string;
  characters: string[];
  narration: string;
  dialogue: DialogueLine[];
}

interface StoryData {
  title: string;
  language: string;
  scenes: StoryScene[];
  all_characters: string[];
}

const API_BASE_URL = 'http://localhost:5679';

// Convert Google Cloud Storage gs:// URLs to HTTPS URLs
const convertGcsUriToHttps = (gcsUri: string): string => {
  if (gcsUri.startsWith('gs://')) {
    // Convert gs://bucket-name/path to https://storage.cloud.google.com/bucket-name/path
    return gcsUri.replace('gs://', 'https://storage.cloud.google.com/');
  }
  return gcsUri;
};

export const useStoryGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { saveImageMetadata } = useImageStorage();

  const generateStory = async (formData: StoryFormData) => {
    setIsGenerating(true);
    setError(null);

    try {
      console.log('Calling external story creation API...');

      // Extract character names and combine descriptions
      const characterNames = formData.characters.map(char => char.name);
      const characterDescriptions = formData.characters
        .map((char) => `${char.name}: ${char.description}`)
        .join('. ');

      const response = await fetch(`${API_BASE_URL}/story_creator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          general_story_input: formData.setting,
          plot: formData.plotIdea,
          target_age_group: formData.ageGroup,
          characters: characterNames,
          character_general_description: characterDescriptions,
          language: formData.language,
          num_scenes: formData.numPages
        })
      });

      if (!response.ok) {
        throw new Error(`Story generation failed: ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log('Raw API response:', responseData);

      // Parse the story data which is returned as a JSON string in the 'story' field
      let storyData: StoryData;
      try {
        if (!responseData.story) {
          throw new Error('No story field found in API response');
        }
        storyData = JSON.parse(responseData.story);
        console.log('Parsed story data:', storyData);
      } catch (parseError) {
        console.error('Failed to parse story JSON:', parseError);
        console.error('Raw response:', responseData);
        throw new Error('Failed to parse story data from API response');
      }

      // Convert scenes to pages format for compatibility with existing components
      const pages = storyData.scenes.map((scene) => ({
        text: scene.narration,
        sceneDescription: `${scene.location}: ${scene.narration.substring(0, 100)}...`
      }));

      // Create a combined story text from all scenes
      const storyText = storyData.scenes.map(scene => scene.narration).join('\n\n');

      return {
        storyText,
        pages,
        storyData // Include the full structured story data
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Story generation error:', err);
      throw err;
    } finally {
      setIsGenerating(false);
    }
  };

  const generateStyleGuide = async (storyData: StoryData, formData: StoryFormData, selectedStyle: string) => {
    try {
      console.log('Generating style guide...');

      // Create character descriptions from form data if available
      const characterDescriptions = formData.characters
        .map((char) => `${char.name}: ${char.description}`)
        .join('. ');

      const response = await fetch(`${API_BASE_URL}/style_creator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: storyData.title,
          general_story_input: storyData.scenes.map(scene => scene.narration).join(' '),
          characters: storyData.all_characters,
          character_general_description: characterDescriptions,
          illustration_style: selectedStyle
        })
      });

      if (!response.ok) {
        throw new Error(`Style guide generation failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.style_guide;

    } catch (err) {
      console.error('Style guide generation error:', err);
      throw err;
    }
  };

  const generateImages = async (
    pages: StoryPage[],
    storyData: StoryData,
    formData: StoryFormData,
    selectedStyle: string,
    storyId?: string
  ) => {
    setIsGeneratingImages(true);
    setError(null);

    try {
      console.log('Generating style guide and images...');

      // First generate the style guide
      const styleGuide = await generateStyleGuide(storyData, formData, selectedStyle);

      const generatedImages: string[] = [];

      // Generate images for each page
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const sceneId = `scene_${i + 1}`;

        let imageResponse;

        if (i === 0) {
          // First scene
          const nextScenePrompt = i + 1 < pages.length ? pages[i + 1].sceneDescription || '' : '';
          imageResponse = await fetch(`${API_BASE_URL}/first_scene`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              next_scene_prompt: nextScenePrompt,
              style_guide: styleGuide
            })
          });
        } else if (i === pages.length - 1) {
          // Last scene
          const previousScenePrompt = pages[i - 1].sceneDescription || '';
          imageResponse = await fetch(`${API_BASE_URL}/last_scene`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              previous_scene_prompt: previousScenePrompt,
              style_guide: styleGuide
            })
          });
        } else {
          // Middle scenes
          const previousScenePrompt = pages[i - 1].sceneDescription || '';
          const nextScenePrompt = i + 1 < pages.length ? pages[i + 1].sceneDescription || '' : '';
          imageResponse = await fetch(`${API_BASE_URL}/other_scenes`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              previous_scene_prompt: previousScenePrompt,
              next_scene_prompt: nextScenePrompt,
              style_guide: styleGuide
            })
          });
        }

        if (!imageResponse.ok) {
          throw new Error(`Image generation failed for scene ${i + 1}: ${imageResponse.statusText}`);
        }

        const imageData = await imageResponse.json();
        console.log(`Image API response for scene ${i + 1}:`, imageData);

        // Convert GCS URI to HTTPS URL
        const httpsUrl = convertGcsUriToHttps(imageData.gcs_uri);
        generatedImages.push(httpsUrl);

        // Save image metadata to database if storyId is provided
        if (storyId) {
          try {
            await saveImageMetadata(storyId, sceneId, httpsUrl, i + 1);
            console.log(`Image metadata saved for scene ${i + 1}`);
          } catch (metadataError) {
            console.error(`Failed to save image metadata for scene ${i + 1}:`, metadataError);
            // Don't throw here as the image generation was successful
          }
        }

        console.log(`Generated image ${i + 1}/${pages.length}: ${imageData.filename}`);
        console.log(`Original GCS URI: ${imageData.gcs_uri}`);
        console.log(`Converted HTTPS URL: ${httpsUrl}`);
      }

      return generatedImages.map((imageUrl) => ({
        imageUrl
      }));

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Image generation error:', err);
      throw err;
    } finally {
      setIsGeneratingImages(false);
    }
  };

  const saveStory = async (
    formData: StoryFormData,
    storyText: string,
    pages: StoryPage[],
    style: string,
    storyData?: StoryData
  ) => {
    try {
      console.log('Saving story to Supabase...');

      // Convert the pages array to Json type for Supabase
      const pagesAsJson: Json = pages as unknown as Json;

      // Get the main character name (first character if multiple)
      const mainCharacter = formData.characters.length > 0 ? formData.characters[0].name : '';

      // Prepare the story data for storage
      const storyRecord = {
        title: formData.title,
        main_character: mainCharacter,
        setting: formData.setting,
        plot_idea: formData.plotIdea,
        age_group: formData.ageGroup,
        style: style,
        story_text: storyText,
        pages: pagesAsJson,
        status: 'completed'
      };

      console.log('Story record to save:', storyRecord);

      const { data, error } = await supabase
        .from('user_stories')
        .insert(storyRecord)
        .select()
        .single();

      if (error) {
        console.error('Supabase error:', error);
        throw new Error(error.message);
      }

      console.log('Story saved successfully:', data);

      // Also save to childrens_book table with structured data if available
      if (storyData) {
        await saveStructuredStory(data.id, storyData, style);
      }

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save story';
      console.error('Save story error:', err);
      setError(errorMessage);
      throw err;
    }
  };

  const saveStructuredStory = async (storyId: string, storyData: StoryData, style: string) => {
    try {
      console.log('Saving structured story data...');

      const structuredData: Json = {
        story_id: storyId,
        title: storyData.title,
        language: storyData.language,
        scenes: storyData.scenes,
        all_characters: storyData.all_characters
      } as unknown as Json;

      const { error } = await supabase
        .from('childrens_book')
        .insert({
          structured_story: structuredData,
          style: style
        });

      if (error) {
        console.error('Error saving structured story:', error);
        // Don't throw here as the main story is already saved
      } else {
        console.log('Structured story saved successfully');
      }
    } catch (err) {
      console.error('Error saving structured story:', err);
      // Don't throw here as the main story is already saved
    }
  };

  const saveStoryWithImages = async (
    formData: StoryFormData,
    storyText: string,
    pages: StoryPage[],
    style: string,
    storyData: StoryData
  ) => {
    try {
      console.log('Starting comprehensive story save with images...');

      // First save the story to get the ID
      const savedStory = await saveStory(formData, storyText, pages, style, storyData);

      // Then generate and save images with the story ID
      const imagesWithMetadata = await generateImages(pages, storyData, formData, style, savedStory.id);

      // Update the story with the generated images
      const updatedPages = pages.map((page, index) => ({
        ...page,
        imageUrl: imagesWithMetadata[index]?.imageUrl || '/placeholder.svg'
      }));

      // Update the story record with the new pages containing image URLs
      const { error: updateError } = await supabase
        .from('user_stories')
        .update({
          pages: updatedPages as unknown as Json
        })
        .eq('id', savedStory.id);

      if (updateError) {
        console.error('Failed to update story with image URLs:', updateError);
        // Don't throw here as the story and images are saved
      }

      return {
        ...savedStory,
        pages: updatedPages
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save story with images';
      console.error('Save story with images error:', err);
      setError(errorMessage);
      throw err;
    }
  };

  return {
    generateStory,
    generateImages,
    generateStyleGuide,
    saveStory,
    saveStoryWithImages,
    isGenerating,
    isGeneratingImages,
    error
  };
};

export type { StoryData, StoryScene, DialogueLine };
