import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { Json } from '@/integrations/supabase/types';

interface Character {
  name: string;
  description: string;
}

interface StoryFormData {
  title: string;
  characters: Character[];
  setting: string;
  plotIdea: string;
  ageGroup: string;
  language: string;
  numPages: number;
}

interface StoryPage {
  text: string;
  sceneDescription?: string;
  imageUrl?: string;
}

const API_BASE_URL = 'http://localhost:5679';

export const useStoryGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateStory = async (formData: StoryFormData) => {
    setIsGenerating(true);
    setError(null);

    try {
      console.log('Calling external story creation API...');
      
      // Extract character names and combine descriptions
      const characterNames = formData.characters.map(char => char.name);
      const characterDescriptions = formData.characters
        .map((char, index) => `${char.name}: ${char.description}`)
        .join('. ');
      
      const response = await fetch(`${API_BASE_URL}/story_creator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          general_story_input: formData.setting,
          plot: formData.plotIdea,
          target_age_group: formData.ageGroup,
          characters: characterNames,
          character_general_description: characterDescriptions,
          language: formData.language,
          num_scenes: formData.numPages
        })
      });

      if (!response.ok) {
        throw new Error(`Story generation failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Parse the story into pages (assuming the API returns a story with page breaks)
      const storyText = data.story;
      const pages = storyText.split('\n\n').filter((page: string) => page.trim()).map((pageText: string, index: number) => ({
        text: pageText.trim(),
        sceneDescription: `Scene ${index + 1}: ${pageText.substring(0, 100)}...`
      }));

      return {
        storyText,
        pages
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Story generation error:', err);
      throw err;
    } finally {
      setIsGenerating(false);
    }
  };

  const generateStyleGuide = async (formData: StoryFormData) => {
    try {
      console.log('Generating style guide...');
      
      const characterNames = formData.characters.map(char => char.name);
      
      const response = await fetch(`${API_BASE_URL}/style_creator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          general_story_input: formData.plotIdea,
          characters: characterNames
        })
      });

      if (!response.ok) {
        throw new Error(`Style guide generation failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.style_guide;

    } catch (err) {
      console.error('Style guide generation error:', err);
      throw err;
    }
  };

  const generateImages = async (pages: StoryPage[], style: string, mainCharacter: string, formData: StoryFormData) => {
    setIsGeneratingImages(true);
    setError(null);

    try {
      console.log('Generating style guide and images...');
      
      // First generate the style guide
      const styleGuide = await generateStyleGuide(formData);
      
      const generatedImages: string[] = [];

      // Generate images for each page
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const sceneId = `scene_${i + 1}`;
        
        let imageResponse;
        
        if (i === 0) {
          // First scene
          const nextScenePrompt = i + 1 < pages.length ? pages[i + 1].sceneDescription || '' : '';
          imageResponse = await fetch(`${API_BASE_URL}/first_scene`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              next_scene_prompt: nextScenePrompt,
              style_guide: styleGuide
            })
          });
        } else if (i === pages.length - 1) {
          // Last scene
          const previousScenePrompt = pages[i - 1].sceneDescription || '';
          imageResponse = await fetch(`${API_BASE_URL}/last_scene`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              previous_scene_prompt: previousScenePrompt,
              style_guide: styleGuide
            })
          });
        } else {
          // Middle scenes
          const previousScenePrompt = pages[i - 1].sceneDescription || '';
          const nextScenePrompt = i + 1 < pages.length ? pages[i + 1].sceneDescription || '' : '';
          imageResponse = await fetch(`${API_BASE_URL}/other_scenes`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              previous_scene_prompt: previousScenePrompt,
              next_scene_prompt: nextScenePrompt,
              style_guide: styleGuide
            })
          });
        }

        if (!imageResponse.ok) {
          throw new Error(`Image generation failed for scene ${i + 1}: ${imageResponse.statusText}`);
        }

        const imageData = await imageResponse.json();
        generatedImages.push(imageData.gcs_uri);
        
        console.log(`Generated image ${i + 1}/${pages.length}: ${imageData.filename}`);
      }

      return generatedImages.map((imageUrl, index) => ({
        imageUrl
      }));

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Image generation error:', err);
      throw err;
    } finally {
      setIsGeneratingImages(false);
    }
  };

  const saveStory = async (formData: StoryFormData, storyText: string, pages: StoryPage[], style: string) => {
    try {
      // Convert the pages array to Json type for Supabase by casting through unknown
      const pagesAsJson: Json = pages as unknown as Json;
      
      // Get the main character name (first character if multiple)
      const mainCharacter = formData.characters.length > 0 ? formData.characters[0].name : '';

      const { data, error } = await supabase
        .from('user_stories')
        .insert({
          title: formData.title,
          main_character: mainCharacter,
          setting: formData.setting,
          plot_idea: formData.plotIdea,
          age_group: formData.ageGroup,
          style: style,
          story_text: storyText,
          pages: pagesAsJson,
          status: 'completed'
        })
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save story';
      setError(errorMessage);
      throw err;
    }
  };

  return {
    generateStory,
    generateImages,
    saveStory,
    isGenerating,
    isGeneratingImages,
    error
  };
};
