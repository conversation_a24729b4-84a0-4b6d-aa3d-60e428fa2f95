import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { <PERSON>son } from '@/integrations/supabase/types';

interface Character {
  name: string;
  description: string;
}

interface StoryFormData {
  title: string;
  characters: Character[];
  setting: string;
  plotIdea: string;
  ageGroup: string;
  language: string;
  numPages: number;
}

interface StoryPage {
  text: string;
  sceneDescription?: string;
  imageUrl?: string;
}

interface DialogueLine {
  speaker: string;
  line: string;
}

interface StoryScene {
  scene_id: string;
  location: string;
  characters: string[];
  narration: string;
  dialogue: DialogueLine[];
}

interface StoryData {
  title: string;
  language: string;
  scenes: StoryScene[];
  all_characters: string[];
}

const API_BASE_URL = 'http://localhost:5679';

export const useStoryGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateStory = async (formData: StoryFormData) => {
    setIsGenerating(true);
    setError(null);

    try {
      console.log('Calling external story creation API...');

      // Extract character names and combine descriptions
      const characterNames = formData.characters.map(char => char.name);
      const characterDescriptions = formData.characters
        .map((char) => `${char.name}: ${char.description}`)
        .join('. ');

      const response = await fetch(`${API_BASE_URL}/story_creator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          general_story_input: formData.setting,
          plot: formData.plotIdea,
          target_age_group: formData.ageGroup,
          characters: characterNames,
          character_general_description: characterDescriptions,
          language: formData.language,
          num_scenes: formData.numPages
        })
      });

      if (!response.ok) {
        throw new Error(`Story generation failed: ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log('Raw API response:', responseData);

      // Parse the story data which is returned as a JSON string in the 'story' field
      let storyData: StoryData;
      try {
        if (!responseData.story) {
          throw new Error('No story field found in API response');
        }
        storyData = JSON.parse(responseData.story);
        console.log('Parsed story data:', storyData);
      } catch (parseError) {
        console.error('Failed to parse story JSON:', parseError);
        console.error('Raw response:', responseData);
        throw new Error('Failed to parse story data from API response');
      }

      // Convert scenes to pages format for compatibility with existing components
      const pages = storyData.scenes.map((scene) => ({
        text: scene.narration,
        sceneDescription: `${scene.location}: ${scene.narration.substring(0, 100)}...`
      }));

      // Create a combined story text from all scenes
      const storyText = storyData.scenes.map(scene => scene.narration).join('\n\n');

      return {
        storyText,
        pages,
        storyData // Include the full structured story data
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Story generation error:', err);
      throw err;
    } finally {
      setIsGenerating(false);
    }
  };

  const generateStyleGuide = async (storyData: StoryData, formData: StoryFormData) => {
    try {
      console.log('Generating style guide...');

      // Create character descriptions from form data if available
      const characterDescriptions = formData.characters
        .map((char) => `${char.name}: ${char.description}`)
        .join('. ');

      const response = await fetch(`${API_BASE_URL}/style_creator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: storyData.title,
          general_story_input: storyData.scenes.map(scene => scene.narration).join(' '),
          characters: storyData.all_characters,
          character_general_description: characterDescriptions
        })
      });

      if (!response.ok) {
        throw new Error(`Style guide generation failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.style_guide;

    } catch (err) {
      console.error('Style guide generation error:', err);
      throw err;
    }
  };

  const generateImages = async (pages: StoryPage[], storyData: StoryData, formData: StoryFormData) => {
    setIsGeneratingImages(true);
    setError(null);

    try {
      console.log('Generating style guide and images...');

      // First generate the style guide
      const styleGuide = await generateStyleGuide(storyData, formData);

      const generatedImages: string[] = [];

      // Generate images for each page
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const sceneId = `scene_${i + 1}`;

        let imageResponse;

        if (i === 0) {
          // First scene
          const nextScenePrompt = i + 1 < pages.length ? pages[i + 1].sceneDescription || '' : '';
          imageResponse = await fetch(`${API_BASE_URL}/first_scene`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              next_scene_prompt: nextScenePrompt,
              style_guide: styleGuide
            })
          });
        } else if (i === pages.length - 1) {
          // Last scene
          const previousScenePrompt = pages[i - 1].sceneDescription || '';
          imageResponse = await fetch(`${API_BASE_URL}/last_scene`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              previous_scene_prompt: previousScenePrompt,
              style_guide: styleGuide
            })
          });
        } else {
          // Middle scenes
          const previousScenePrompt = pages[i - 1].sceneDescription || '';
          const nextScenePrompt = i + 1 < pages.length ? pages[i + 1].sceneDescription || '' : '';
          imageResponse = await fetch(`${API_BASE_URL}/other_scenes`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title: formData.title,
              scene_id: sceneId,
              current_scene_prompt: page.sceneDescription || page.text,
              previous_scene_prompt: previousScenePrompt,
              next_scene_prompt: nextScenePrompt,
              style_guide: styleGuide
            })
          });
        }

        if (!imageResponse.ok) {
          throw new Error(`Image generation failed for scene ${i + 1}: ${imageResponse.statusText}`);
        }

        const imageData = await imageResponse.json();
        generatedImages.push(imageData.gcs_uri);

        console.log(`Generated image ${i + 1}/${pages.length}: ${imageData.filename}`);
      }

      return generatedImages.map((imageUrl) => ({
        imageUrl
      }));

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Image generation error:', err);
      throw err;
    } finally {
      setIsGeneratingImages(false);
    }
  };

  const saveStory = async (formData: StoryFormData, storyText: string, pages: StoryPage[], style: string) => {
    try {
      // Convert the pages array to Json type for Supabase by casting through unknown
      const pagesAsJson: Json = pages as unknown as Json;

      // Get the main character name (first character if multiple)
      const mainCharacter = formData.characters.length > 0 ? formData.characters[0].name : '';

      const { data, error } = await supabase
        .from('user_stories')
        .insert({
          title: formData.title,
          main_character: mainCharacter,
          setting: formData.setting,
          plot_idea: formData.plotIdea,
          age_group: formData.ageGroup,
          style: style,
          story_text: storyText,
          pages: pagesAsJson,
          status: 'completed'
        })
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save story';
      setError(errorMessage);
      throw err;
    }
  };

  return {
    generateStory,
    generateImages,
    generateStyleGuide,
    saveStory,
    isGenerating,
    isGeneratingImages,
    error
  };
};

export type { StoryData, StoryScene, DialogueLine };
