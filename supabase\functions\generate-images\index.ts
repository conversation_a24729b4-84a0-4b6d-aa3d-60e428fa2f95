
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not found');
    }

    const { pages, style, mainCharacter } = await req.json();

    // Style prompts for different illustration styles
    const stylePrompts = {
      'watercolor': 'watercolor painting style, soft and flowing, gentle textures',
      'cartoon': 'cartoon illustration style, bright colors, friendly and expressive characters',
      'papercut': 'paper cutout style, layered paper craft appearance, clean geometric shapes',
      'pencil': 'pencil sketch style, hand-drawn appearance, classic storybook illustration',
      '3d': '3D rendered illustration, soft lighting, child-friendly 3D characters',
      'collage': 'mixed media collage style, combining various textures and materials'
    };

    const stylePrompt = stylePrompts[style as keyof typeof stylePrompts] || stylePrompts['cartoon'];

    const generatedImages = [];

    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      
      const imagePrompt = `Children's book illustration, ${stylePrompt}, featuring ${mainCharacter}. Scene: ${page.sceneDescription || page.text}. Child-friendly, warm and inviting, suitable for a children's storybook. High quality illustration.`;

      console.log(`Generating image ${i + 1} of ${pages.length}...`);

      const response = await fetch('https://api.openai.com/v1/images/generations', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openAIApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'dall-e-3',
          prompt: imagePrompt,
          n: 1,
          size: '1024x1024',
          quality: 'standard',
        }),
      });

      if (!response.ok) {
        console.error(`Failed to generate image ${i + 1}:`, response.status);
        generatedImages.push({ imageUrl: '/placeholder.svg', error: 'Failed to generate image' });
        continue;
      }

      const imageData = await response.json();
      generatedImages.push({
        imageUrl: imageData.data[0].url,
        error: null
      });
    }

    return new Response(JSON.stringify({
      success: true,
      images: generatedImages
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-images function:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Failed to generate images'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
