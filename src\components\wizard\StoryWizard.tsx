import React, { useState } from 'react';
import StepIndicator from './StepIndicator';
import IdeaCaptureStep from './IdeaCaptureStep';
import StoryDraftStep from './StoryDraftStep';
import IllustrationStyleStep from './IllustrationStyleStep';
import StoryPreviewStep from './StoryPreviewStep';
import StorySummaryStep from './StorySummaryStep';

interface Character {
  name: string;
  description: string;
}

interface StoryWizardProps {
  onComplete: () => void;
}

const StoryWizard = ({ onComplete }: StoryWizardProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    title: '',
    characters: [] as Character[],
    setting: '',
    plotIdea: '',
    ageGroup: '',
    numPages: 8,
    language: '',
  });
  const [selectedStyle, setSelectedStyle] = useState('');
  const [storyData, setStoryData] = useState<any>(null);
  const [savedStory, setSavedStory] = useState<any>(null);

  const handleNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleStyleSelect = (style: string) => {
    setSelectedStyle(style);
    handleNext();
  };

  const handleStoryGenerated = (generatedStoryData: any) => {
    setStoryData(generatedStoryData);
    handleNext();
  };

  const handleStoryFinished = (completeStoryData: any) => {
    setSavedStory(completeStoryData);
    handleNext();
  };

  const handleStartNew = () => {
    setCurrentStep(0);
    setFormData({
      title: '',
      characters: [],
      setting: '',
      plotIdea: '',
      ageGroup: '',
      numPages: 8,
      language: '',
    });
    setSelectedStyle('');
    setStoryData(null);
    setSavedStory(null);
  };

  const totalSteps = 5;

  // Get the main character name for components that still need it
  const mainCharacter = formData.characters.length > 0 ? formData.characters[0].name : '';

  return (
    <div className="py-8 px-4 sm:px-6">
      {currentStep < 4 && <StepIndicator currentStep={currentStep} totalSteps={totalSteps - 1} />}
      
      {currentStep === 0 && (
        <IdeaCaptureStep 
          formData={formData} 
          setFormData={setFormData} 
          onNext={handleNext} 
        />
      )}
      
      {currentStep === 1 && (
        <StoryDraftStep 
          formData={formData} 
          onBack={handleBack} 
          onNext={handleStoryGenerated} 
        />
      )}
      
      {currentStep === 2 && (
        <IllustrationStyleStep 
          onBack={handleBack} 
          onNext={handleStyleSelect} 
        />
      )}
      
      {currentStep === 3 && storyData && (
        <StoryPreviewStep 
          formData={formData}
          storyData={storyData}
          selectedStyle={selectedStyle}
          onBack={handleBack}
          onFinish={handleStoryFinished}
        />
      )}
      
      {currentStep === 4 && savedStory && (
        <StorySummaryStep 
          formData={{
            ...formData,
            mainCharacter
          }}
          selectedStyle={selectedStyle}
          onStartNew={handleStartNew}
          onViewLibrary={onComplete}
        />
      )}
    </div>
  );
};

export default StoryWizard;
