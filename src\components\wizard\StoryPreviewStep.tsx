import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, ArrowRight, Printer, Download, BookOpen, RefreshCw } from 'lucide-react';
import { useStoryGeneration, type StoryData } from '@/hooks/useStoryGeneration';
import { useToast } from '@/hooks/use-toast';

interface Character {
  name: string;
  description: string;
}

interface StoryPage {
  text: string;
  sceneDescription?: string;
  imageUrl?: string;
}

interface StoryPreviewStepProps {
  formData: {
    title: string;
    characters: Character[];
    setting: string;
    plotIdea: string;
    ageGroup: string;
    numPages: number;
    language: string;
  };
  storyData: {
    storyText: string;
    pages: StoryPage[];
    storyData: StoryData;
  };
  selectedStyle: string;
  onBack: () => void;
  onFinish: (completeStoryData: any) => void;
}

const StoryPreviewStep = ({ formData, storyData, selectedStyle, onBack, onFinish }: StoryPreviewStepProps) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [pages, setPages] = useState<StoryPage[]>([]);
  const { generateImages, saveStory, isGeneratingImages, error } = useStoryGeneration();
  const { toast } = useToast();

  const handleGenerateImages = async () => {
    try {
      const images = await generateImages(storyData.pages, storyData.storyData, formData);

      const pagesWithImages = storyData.pages.map((page, index) => ({
        ...page,
        imageUrl: images[index]?.imageUrl || '/placeholder.svg'
      }));

      setPages(pagesWithImages);
    } catch (err) {
      toast({
        title: "Image Generation Failed",
        description: "Using placeholder images. You can try regenerating images later.",
        variant: "destructive",
      });
      // Use placeholder images as fallback
      setPages(storyData.pages.map(page => ({ ...page, imageUrl: '/placeholder.svg' })));
    }
  };

  const handleFinishStory = async () => {
    try {
      const savedStory = await saveStory(formData, storyData.storyText, pages, selectedStyle);
      onFinish(savedStory);
    } catch (err) {
      toast({
        title: "Save Failed",
        description: "Failed to save your story. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Auto-generate images when component mounts
  useEffect(() => {
    handleGenerateImages();
  }, []);

  const nextPage = () => {
    if (currentPage < pages.length - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-4xl mx-auto"
    >
      <Card className="border-2 border-pink/30 shadow-story rounded-xl overflow-hidden">
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold text-slate-800 mb-6 text-center">
            <span className="emoji-hint">📖</span> Story Preview
          </h2>

          {isGeneratingImages ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-muted rounded-full"></div>
                <div className="w-16 h-16 border-t-4 border-pink animate-rotate-slow absolute top-0 rounded-full"></div>
              </div>
              <p className="mt-6 text-muted-foreground">Creating your illustrated story...</p>
              <p className="mt-2 text-sm text-muted-foreground">✨ Generating personalized style guide</p>
              <p className="mt-1 text-sm text-muted-foreground">🎨 Creating {storyData.pages.length} beautiful illustrations</p>
            </div>
          ) : pages.length > 0 ? (
            <>
              <div className="flex flex-col items-center bg-white rounded-xl border border-slate-200 p-4 mb-6 relative aspect-[5/4] overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center bg-slate-50">
                  <div className="w-full max-w-md">
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={currentPage}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.3 }}
                        className="flex flex-col items-center"
                      >
                        <div className="bg-white shadow-md rounded-lg overflow-hidden mb-4 w-full">
                          <img
                            src={pages[currentPage]?.imageUrl || '/placeholder.svg'}
                            alt={`Page ${currentPage + 1}`}
                            className="w-full aspect-[4/3] object-cover"
                          />
                        </div>
                        <div className="p-4 text-center">
                          <p className="text-lg">{pages[currentPage]?.text}</p>
                        </div>
                      </motion.div>
                    </AnimatePresence>
                  </div>
                </div>
              </div>

              <div className="flex justify-center items-center space-x-3 mb-8">
                <Button
                  onClick={prevPage}
                  variant="outline"
                  disabled={currentPage === 0}
                  className="h-10 w-10 rounded-full p-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium">
                  Page {currentPage + 1} of {pages.length}
                </span>
                <Button
                  onClick={nextPage}
                  variant="outline"
                  disabled={currentPage === pages.length - 1}
                  className="h-10 w-10 rounded-full p-0"
                >
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-3 justify-center mb-6">
                <Button
                  onClick={handleGenerateImages}
                  variant="outline"
                  className="flex items-center"
                  disabled={isGeneratingImages}
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Regenerate Images
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Printer className="mr-2 h-4 w-4" /> Print
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Download className="mr-2 h-4 w-4" /> Download PDF
                </Button>
              </div>

              <div className="flex justify-between pt-4">
                <Button
                  onClick={onBack}
                  variant="outline"
                  className="h-12 px-6"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back
                </Button>
                <Button
                  onClick={handleFinishStory}
                  className="bg-pink hover:bg-pink-dark text-white h-12 px-6"
                >
                  Finish & Save Story
                </Button>
              </div>
            </>
          ) : null}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default StoryPreviewStep;
