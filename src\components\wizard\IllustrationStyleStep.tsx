
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useStoryGeneration, type StoryData } from '@/hooks/useStoryGeneration';
import { useToast } from '@/hooks/use-toast';

interface StyleOption {
  id: string;
  name: string;
  description: string;
  previewUrl: string;
}

interface IllustrationStyleStepProps {
  storyData: StoryData;
  onBack: () => void;
  onNext: (selectedStyle: string) => void;
}

const styleOptions: StyleOption[] = [
  {
    id: 'watercolor',
    name: 'Watercolor Dreams',
    description: 'Soft, flowing watercolor illustrations with gentle textures',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'cartoon',
    name: 'Playful Cartoon',
    description: 'Bright, bold cartoon characters with expressive features',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'papercut',
    name: 'Paper Cutouts',
    description: 'Charming illustrations that look like layered paper cutouts',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'pencil',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Hand-drawn pencil illustrations with a classic storybook feel',
    previewUrl: '/placeholder.svg'
  },
  {
    id: '3d',
    name: '3D Wonderland',
    description: 'Modern 3D characters and scenes with depth and dimension',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'collage',
    name: 'Mixed Media',
    description: 'Creative collage style combining photos, drawings, and textures',
    previewUrl: '/placeholder.svg'
  }
];

const IllustrationStyleStep = ({ storyData, onBack, onNext }: IllustrationStyleStepProps) => {
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [isGeneratingStyleGuide, setIsGeneratingStyleGuide] = useState(false);
  const [styleGuide, setStyleGuide] = useState<string | null>(null);
  const { generateStyleGuide } = useStoryGeneration();
  const { toast } = useToast();

  // Generate style guide when component mounts
  useEffect(() => {
    const generateGuide = async () => {
      setIsGeneratingStyleGuide(true);
      try {
        const guide = await generateStyleGuide(storyData);
        setStyleGuide(guide);
        console.log('Style guide generated:', guide);
      } catch (error) {
        console.error('Failed to generate style guide:', error);
        toast({
          title: "Style Guide Generation Failed",
          description: "Continuing with default style options.",
          variant: "destructive",
        });
      } finally {
        setIsGeneratingStyleGuide(false);
      }
    };

    generateGuide();
  }, [storyData, generateStyleGuide, toast]);

  const handleStyleSelect = (styleId: string) => {
    setSelectedStyle(styleId);
  };

  const handleContinue = () => {
    if (selectedStyle) {
      onNext(selectedStyle);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-4xl mx-auto"
    >
      <Card className="border-2 border-purple/30 shadow-story rounded-xl overflow-hidden">
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold text-slate-800 mb-6 text-center">
            <span className="emoji-hint">🎨</span> Choose Your Illustration Style
          </h2>

          {isGeneratingStyleGuide && (
            <div className="flex flex-col items-center justify-center py-8 mb-6">
              <div className="relative">
                <div className="w-12 h-12 border-4 border-muted rounded-full"></div>
                <div className="w-12 h-12 border-t-4 border-purple animate-rotate-slow absolute top-0 rounded-full"></div>
              </div>
              <p className="mt-4 text-muted-foreground">Creating personalized style guide...</p>
            </div>
          )}

          {styleGuide && (
            <div className="bg-purple/10 border border-purple/20 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-purple-dark mb-2">✨ Personalized Style Guide Created</h3>
              <p className="text-sm text-slate-600">
                A custom style guide has been generated based on your story's characters and themes.
                This will ensure consistent and beautiful illustrations throughout your book.
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            {styleOptions.map((style, index) => (
              <motion.div
                key={style.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`style-chip ${selectedStyle === style.id ? 'active' : ''}`}
                onClick={() => handleStyleSelect(style.id)}
              >
                <div className="aspect-[4/3] bg-slate-100 rounded-lg mb-3 overflow-hidden">
                  <img
                    src={style.previewUrl}
                    alt={style.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="font-semibold">{style.name}</h3>
                <p className="text-sm text-slate-600">{style.description}</p>
              </motion.div>
            ))}
          </div>

          <div className="flex justify-between pt-4">
            <Button
              onClick={onBack}
              variant="outline"
              className="h-12 px-6"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            <Button
              onClick={handleContinue}
              disabled={!selectedStyle}
              className="bg-purple hover:bg-purple-dark text-white h-12 px-6"
            >
              Preview Story <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default IllustrationStyleStep;
